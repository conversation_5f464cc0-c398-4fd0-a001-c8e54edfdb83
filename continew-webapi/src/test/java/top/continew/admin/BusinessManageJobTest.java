package top.continew.admin;

import cn.crane4j.core.support.OperateTemplate;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.aizuda.snailjob.client.job.core.dto.JobArgs;
import jakarta.annotation.Resource;
import org.apache.commons.beanutils.BeanUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import top.continew.admin.biz.model.entity.SalesPersonnelMonthlyDataDO;
import top.continew.admin.biz.model.resp.SalesPersonnelMonthlyDataResp;
import top.continew.admin.biz.service.SalesPersonnelMonthlyDataService;
import top.continew.admin.job.BusinessManageJob;
import top.continew.admin.system.enums.JobRankEnum;

import java.util.ArrayList;
import java.util.List;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/3/17 17:26
 */
@SpringBootTest
public class BusinessManageJobTest {
    @Resource
    private SalesPersonnelMonthlyDataService salesPersonnelMonthlyDataService;

    @Test
    public void test() {
        List<SalesPersonnelMonthlyDataDO> list = salesPersonnelMonthlyDataService.listByUserIds(List.of(667322803787383450L), JobRankEnum.PROBATION);
        OperateTemplate operateTemplate = SpringUtil.getBean(OperateTemplate.class);
        List<SalesPersonnelMonthlyDataResp> resp = new ArrayList<>();
        for (SalesPersonnelMonthlyDataDO salesPersonnelMonthlyDataDO : list) {
            SalesPersonnelMonthlyDataResp salesPersonnelMonthlyDataResp = new SalesPersonnelMonthlyDataResp();
            BeanUtil.copyProperties(salesPersonnelMonthlyDataDO,salesPersonnelMonthlyDataResp);
            resp.add(salesPersonnelMonthlyDataResp);
        }
        operateTemplate.execute(resp);
        System.out.println(resp);
    }
}
