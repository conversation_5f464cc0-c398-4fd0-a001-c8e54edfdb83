package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.common.base.BaseResp;

/**
 * 商务人员月度绩效数据信息
 *
 * <AUTHOR>
 * @since 2025/08/20 10:43
 */
@Data
@Schema(description = "商务人员月度绩效数据信息")
public class SalesPersonnelMonthlyDataResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联商务
     */
    @Schema(description = "关联商务")
    private Long businessUserId;

    /**
     * 入职时间
     */
    @Schema(description = "入职时间")
    private LocalDate entryTime;

    /**
     * 岗位类型
     */
    @Schema(description = "岗位类型")
    private Integer jobRank;

    /**
     * 统计月份
     */
    @Schema(description = "统计月份")
    private LocalDate salesDate;

    /**
     * 微信好友数量
     */
    @Schema(description = "微信好友数量")
    private Integer wechatFriendsCount;

    /**
     * Telegram好友数量
     */
    @Schema(description = "Telegram好友数量")
    private Integer telegramFriendsCount;

    /**
     * 成交客户数量
     */
    @Schema(description = "成交客户数量")
    private Integer closedCustomersCount;

    /**
     * 交友数量
     */
    @Schema(description = "交友数量")
    private Integer friendsCount;

    /**
     * 意向客户数量
     */
    @Schema(description = "意向客户数量")
    private Integer intendedCustomersCount;

    /**
     * 客户总消耗
     */
    @Schema(description = "客户总消耗")
    private BigDecimal totalCustomerSpend;

    /**
     * 广告户使用率
     */
    @Schema(description = "广告户使用率")
    private Double adAccountUsageRate;

    /**
     * 客户单户平均消耗
     */
    @Schema(description = "客户单户平均消耗")
    private BigDecimal avgSingleAccountSpend;

    /**
     * 客户留存率
     */
    @Schema(description = "客户留存率")
    private Double customerRetentionRate;

    /**
     * 日报漏填写数量
     */
    @Schema(description = "日报漏填写数量")
    private Integer dailyReportMissingCount;

    /**
     * 客咨漏填写数量
     */
    @Schema(description = "客咨漏填写数量")
    private Integer customerConsultingMissingCount;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private Long updateUser;
}