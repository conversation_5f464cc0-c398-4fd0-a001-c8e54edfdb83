package top.continew.admin.biz.gmail;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import top.continew.admin.biz.utils.ThreadPoolHelper;

import javax.mail.*;
import javax.mail.internet.MimeMessage;
import javax.mail.search.*;
import java.io.IOException;
import java.net.SocketException;
import java.net.SocketTimeoutException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Gmail验证码提取助手基类
 * 提供通用的邮件处理和验证码提取功能
 */
@Slf4j
public abstract class BaseGmailHelper {

    @Resource
    protected StringRedisTemplate stringRedisTemplate;

    @Resource
    protected ObjectMapper objectMapper;

    private static final String HOST = "imap.gmail.com";


    /**
     * 获取用于匹配卡号后四位的正则表达式模式
     * @return Pattern 卡号后四位匹配模式
     */
    protected abstract Pattern getCardLastFourPattern();

    /**
     * 获取用于匹配验证码的正则表达式模式
     * @return Pattern 验证码匹配模式
     */
    protected abstract Pattern getVerificationCodePattern();

    /**
     * 获取Redis缓存键
     * @return String Redis键
     */
    protected abstract String getRedisKey();

    /**
     * 获取已处理消息的Redis键
     * @return String 已处理消息的Redis键
     */
    protected abstract String getProcessedMessagesKey();

    /**
     * 获取邮件发送者
     * @return String 邮件发送者
     */
    protected abstract String getEmailSender();

    /**
     * 获取最大缓存大小
     * @return int 最大缓存大小
     */
    protected abstract int getMaxCacheSize();

    protected abstract String getUsername();
    protected abstract String getPassword();
    protected abstract String getTargetSender();


    /**
     * 检查邮件是否为验证码邮件
     * @param message 邮件消息
     * @return boolean 是否为验证码邮件
     */
    protected abstract boolean isVerificationEmail(Message message) throws MessagingException;

    /**
     * 获取邮件内容
     * @param message 邮件消息
     * @return String 邮件内容
     */
    protected String getEmailContent(Message message) throws MessagingException, IOException {
        if (message instanceof MimeMessage) {
            Object content = message.getContent();
            if (content instanceof String) {
                return (String) content;
            } else if (content instanceof Multipart) {
                Multipart multipart = (Multipart) content;
                StringBuilder result = new StringBuilder();
                for (int i = 0; i < multipart.getCount(); i++) {
                    BodyPart bodyPart = multipart.getBodyPart(i);
                    if (bodyPart.getContentType().toLowerCase().contains("text/plain")) {
                        result.append(bodyPart.getContent().toString());
                    }
                }
                return result.toString();
            }
        }
        return "";
    }

    /**
     * 提取验证码
     * @param emailContent 邮件内容
     * @return String 验证码
     */
    protected String extractVerificationCode(String emailContent) {
        Matcher matcher = getVerificationCodePattern().matcher(emailContent);
        if (matcher.find()) {
            return matcher.group();
        }
        return "";
    }

    /**
     * 提取卡号后四位
     * @param emailContent 邮件内容
     * @return String 卡号后四位
     */
    protected String extractCardLastFour(String emailContent) {
        Matcher matcher = getCardLastFourPattern().matcher(emailContent);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return "";
    }

    /**
     * 处理单个邮件消息
     * @param message 邮件消息
     * @param messageId 消息ID
     * @return ValidateCodeResp 提取的验证码信息
     */
    protected ValidateCodeResp processMessage(Message message, String messageId) throws Exception {
        if(!isVerificationEmail(message)) {
            log.info("Email subject does not contain verification code text, skipping...");
            return null;
        }
        String emailContent = getEmailContent(message);
        
        // 子类需要实现具体的验证逻辑
        if (!isVerificationContentValid(emailContent)) {
            log.info("Email content does not contain verification code text, skipping...");
            return null;
        }
        
        String verificationCode = extractVerificationCode(emailContent);
        String cardLastFour = extractCardLastFour(emailContent);
        
        ValidateCodeResp codeRecord = new ValidateCodeResp();
        codeRecord.setMessageId(messageId);
        codeRecord.setCardLastFour(cardLastFour);
        codeRecord.setVerificationCode(verificationCode);
        codeRecord.setReceiveTime(DateUtil.formatDateTime(message.getReceivedDate()));
        codeRecord.setEmailContent(emailContent);
        
        log.info("New verification code processed: {} for card {}", verificationCode, cardLastFour);
        
        return codeRecord;
    }

    /**
     * 验证邮件内容是否有效
     * @param emailContent 邮件内容
     * @return boolean 邮件内容是否有效
     */
    protected abstract boolean isVerificationContentValid(String emailContent);

    /**
     * 批量存储到Redis
     * @param validCodes 有效的验证码列表
     * @param processedIds 已处理的消息ID列表
     */
    protected void batchStoreToRedis(List<ValidateCodeResp> validCodes, List<String> processedIds) {
        try {
            // 批量存储验证码
            List<String> jsonValues = validCodes.stream()
                    .map(code -> {
                        try {
                            return objectMapper.writeValueAsString(code);
                        } catch (Exception e) {
                            log.error("Error serializing code: {}", e.getMessage());
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            
            if (!jsonValues.isEmpty()) {
                stringRedisTemplate.opsForList().leftPushAll(getRedisKey(), jsonValues);
            }
            
            // 批量添加已处理的消息ID
            if (!processedIds.isEmpty()) {
                stringRedisTemplate.opsForSet().add(getProcessedMessagesKey(), processedIds.toArray(new String[0]));
            }
            
        } catch (Exception e) {
            log.error("Error batch storing to Redis: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取最近的验证码记录列表
     * @return 最近的验证码记录，按时间倒序排序
     */
    public List<ValidateCodeResp> getRecentVerificationCodes() {
        Long size = stringRedisTemplate.opsForList().size(getRedisKey());
        if (size == null || size == 0) {
            return new ArrayList<>();
        }
        
        List<String> jsonCodes = stringRedisTemplate.opsForList()
                .range(getRedisKey(), 0, -1);
        
        List<ValidateCodeResp> codes = new ArrayList<>();
        if (jsonCodes != null) {
            for (String jsonCode : jsonCodes) {
                try {
                    ValidateCodeResp code = objectMapper.readValue(jsonCode, ValidateCodeResp.class);
                    codes.add(code);
                } catch (Exception e) {
                    log.error("Error parsing JSON code: {}", jsonCode, e);
                }
            }
            codes.sort((a, b) -> b.getReceiveTime().compareTo(a.getReceiveTime()));
        }
        
        return codes;
    }


    public void checkNewEmails() throws SocketTimeoutException, SocketException {
        Properties props = new Properties();
        props.put("mail.store.protocol", "imap");
        props.put("mail.imap.host", HOST);
        props.put("mail.imap.port", "993");
        props.put("mail.imap.ssl.enable", "true");
        props.put("mail.imap.ssl.trust", "*");

        // 优化连接设置
        props.put("mail.imap.connectiontimeout", "20000");
        props.put("mail.imap.timeout", "15000");
        props.put("mail.imap.connectionpoolsize", "15");
        props.put("mail.imap.connectionpooltimeout", "600000");

        Store store = null;
        Folder inbox = null;
        try {
            Session session = Session.getInstance(props);
            store = session.getStore("imap");
            store.connect(HOST, getUsername(), getPassword());

            inbox = store.getFolder("INBOX");
            inbox.open(Folder.READ_WRITE);

            // 构建复合搜索条件
            SearchTerm fromTerm = new FromStringTerm(getTargetSender());

            // 添加日期过滤
            Date startDay = Date.from(LocalDate.now().minusDays(2).atStartOfDay(ZoneId.systemDefault()).toInstant());
            SearchTerm dateTerm = new ReceivedDateTerm(ComparisonTerm.GE, startDay);

            // 组合搜索条件
            SearchTerm combinedTerm = new AndTerm(fromTerm, dateTerm);
            Message[] messages = inbox.search(combinedTerm);
            log.info("Found {} related messages", messages.length);

            // 限制处理邮件数量
            int maxMessages = Math.min(30, messages.length);
            int startIndex = Math.max(0, messages.length - maxMessages);

            log.info("Processing {} messages from index {} to {}", maxMessages, startIndex, messages.length - 1);

            // 批量检查已处理的消息
            List<String> messageIds = new ArrayList<>();
            for (int i = startIndex; i < messages.length; i++) {
                messageIds.add(messages[i].getMessageNumber() + "");
            }

            // 批量检查Redis中已处理的消息
            Set<String> processedIds = new HashSet<>();
            for (String messageId : messageIds) {
                Boolean isProcessed = stringRedisTemplate.opsForSet().isMember(getProcessedMessagesKey(), messageId);
                if (Boolean.TRUE.equals(isProcessed)) {
                    processedIds.add(messageId);
                }
            }

            // 并发处理邮件
            List<CompletableFuture<ValidateCodeResp>> futures = new ArrayList<>();
            List<String> newProcessedIds = new ArrayList<>();

            ThreadPoolExecutor executor = ThreadPoolHelper.getGmailProcessorInstance();

            for (int i = startIndex; i < messages.length; i++) {
                Message message = messages[i];
                String messageId = message.getMessageNumber() + "";

                if (processedIds.contains(messageId)) {
                    log.info("Message {} has already been processed, skipping...", messageId);
                    continue;
                }

                // 异步处理每封邮件
                CompletableFuture<ValidateCodeResp> future = CompletableFuture.supplyAsync(() -> {
                    try {
                        return processMessage(message, messageId);
                    } catch (Exception e) {
                        log.error("Error processing message {}: {}", messageId, e.getMessage());
                        return null;
                    }
                }, executor);

                futures.add(future);
                newProcessedIds.add(messageId);
            }

            // 批量处理结果
            List<ValidateCodeResp> validCodes = new ArrayList<>();
            for (CompletableFuture<ValidateCodeResp> future : futures) {
                try {
                    ValidateCodeResp result = future.get(30, TimeUnit.SECONDS);
                    if (result != null) {
                        validCodes.add(result);
                    }
                } catch (Exception e) {
                    log.error("Error getting future result: {}", e.getMessage());
                }
            }

            // 批量存储到Redis
            if (!validCodes.isEmpty()) {
                batchStoreToRedis(validCodes, newProcessedIds);
            }

            // 维护缓存大小
            maintainCacheSize();

        } catch (Exception e) {
            log.error("Unexpected error: {}", e.getMessage(), e);
            throw new RuntimeException("调用失败，未知错误", e);
        } finally {
            if (inbox != null) {
                try {
                    inbox.close(false);
                } catch (MessagingException e) {
                    log.warn("Failed to close inbox folder: {}", e.getMessage(), e);
                }
            }

            if (store != null) {
                try {
                    store.close();
                } catch (MessagingException e) {
                    log.warn("Failed to close store: {}", e.getMessage(), e);
                }
            }
        }
    }

    /**
     * 根据卡片尾号查询验证码记录
     * @param cardLastFour 卡片后四位
     * @return 匹配的验证码记录，按接收时间倒序排序
     */
    public List<ValidateCodeResp> getVerificationCodesByCardLastFour(String cardLastFour) {
        if (StrUtil.isBlank(cardLastFour)) {
            return new ArrayList<>();
        }

        List<ValidateCodeResp> allCodes = getRecentVerificationCodes();
        return allCodes.stream()
                .filter(code -> cardLastFour.equals(code.getCardLastFour()))
                .sorted((a, b) -> b.getReceiveTime().compareTo(a.getReceiveTime()))
                .collect(Collectors.toList());
    }

    /**
     * 根据卡片尾号查询最新的验证码记录
     * @param cardLastFour 卡片后四位
     * @return 最新的验证码记录，如果没有找到则返回null
     */
    public ValidateCodeResp getLatestVerificationCodeByCardLastFour(String cardLastFour) {
        if (StrUtil.isBlank(cardLastFour)) {
            return null;
        }

        List<ValidateCodeResp> codes = getVerificationCodesByCardLastFour(cardLastFour);
        return codes.isEmpty() ? null : codes.get(0);
    }

    /**
     * 批量标记邮件为已读
     * @param messages 邮件列表
     */
    protected void batchMarkAsRead(List<Message> messages) throws MessagingException {
        for (Message message : messages) {
            message.setFlag(Flags.Flag.SEEN, true);
        }
    }

    /**
     * 维护缓存大小
     */
    protected void maintainCacheSize() {
        // 维护验证码列表大小
        Long codesSize = stringRedisTemplate.opsForList().size(getRedisKey());
        if (codesSize != null && codesSize > getMaxCacheSize()) {
            stringRedisTemplate.opsForList().trim(getRedisKey(), 0, getMaxCacheSize() - 1);
        }

        // 维护已处理消息ID集合大小
        Long processedSize = stringRedisTemplate.opsForSet().size(getProcessedMessagesKey());
        if (processedSize != null && processedSize > getMaxCacheSize()) {
            // 获取所有消息ID
            Set<String> allMessages = stringRedisTemplate.opsForSet().members(getProcessedMessagesKey());
            if (allMessages != null) {
                List<String> messagesList = new ArrayList<>(allMessages);
                // 按照消息ID排序（假设消息ID是递增的）
                messagesList.sort(String::compareTo);
                // 删除最旧的消息ID
                int toRemove = messagesList.size() - getMaxCacheSize();
                for (int i = 0; i < toRemove; i++) {
                    stringRedisTemplate.opsForSet().remove(getProcessedMessagesKey(), messagesList.get(i));
                }
            }
        }
    }
}