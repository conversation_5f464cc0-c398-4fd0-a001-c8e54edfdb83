/*
 * Copyright (c) 2022-present <PERSON><PERSON>c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import top.continew.starter.core.enums.BaseEnum;

import java.util.Objects;

@Getter
@RequiredArgsConstructor
public enum CardPlatformEnum implements BaseEnum<Integer> {
    HUI_TONG(1, "汇通", false), CARD_VP(2, "cardvp", true), PHOTON_PAY(3, "光子易", true),
    AMZ(4, "AMZ", true), INTERLACE(5, "Interlace", true);

    private final Integer value;
    private final String description;
    private final boolean enable;

    public static String getDescription(Integer value) {
        for (CardPlatformEnum cardPlatformEnum : values()) {
            if (Objects.equals(cardPlatformEnum.getValue(), value)) {
                return cardPlatformEnum.getDescription();
            }
        }
        return null;
    }

}
