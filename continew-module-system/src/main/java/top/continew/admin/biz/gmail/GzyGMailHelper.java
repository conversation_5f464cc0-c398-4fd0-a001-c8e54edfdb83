package top.continew.admin.biz.gmail;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.continew.admin.biz.gmail.config.GzyGMailProperties;

import javax.mail.*;
import java.util.regex.Pattern;

@Slf4j
@Component
public class G<PERSON><PERSON>ailHelper extends BaseGmailHelper {
    private static final String VERIFICATION_CODE_REDIS_KEY = "gmail:verification:codes";
    private static final String PROCESSED_MESSAGES_KEY = "gmail:processed:messages";
    private static final Pattern VERIFICATION_CODE_PATTERN = Pattern.compile("\\b\\d{6}\\b");
    private static final Pattern CARD_LAST_FOUR_PATTERN = Pattern.compile("(\\d+\\*+\\d+)");

    @Resource
    private GzyGMailProperties gmailProperties;

    @Override
    protected Pattern getCardLastFourPattern() {
        return CARD_LAST_FOUR_PATTERN;
    }

    @Override
    protected Pattern getVerificationCodePattern() {
        return VERIFICATION_CODE_PATTERN;
    }

    @Override
    protected String getRedisKey() {
        return VERIFICATION_CODE_REDIS_KEY;
    }

    @Override
    protected String getProcessedMessagesKey() {
        return PROCESSED_MESSAGES_KEY;
    }

    @Override
    protected int getMaxCacheSize() {
        return gmailProperties.getMaxCacheSize();
    }

    @Override
    protected boolean isVerificationEmail(Message message) throws MessagingException {
        String subject = message.getSubject();
        return subject != null && subject.contains("一次性验证码");
    }

    @Override
    protected boolean isVerificationContentValid(String emailContent) {
        return emailContent.contains("一次性验证码");
    }

    @Override
    protected String getEmailSender() {
        return "GZY";
    }

    @Override
    protected String getUsername() {
        return gmailProperties.getUsername();
    }

    @Override
    protected String getPassword() {
        return gmailProperties.getAppPassword();
    }

    @Override
    protected String getTargetSender() {
        return gmailProperties.getTargetSender();
    }
}
