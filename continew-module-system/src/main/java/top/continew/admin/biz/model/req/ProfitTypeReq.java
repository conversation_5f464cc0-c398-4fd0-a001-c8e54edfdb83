package top.continew.admin.biz.model.req;

import java.io.Serial;
import java.time.*;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改利润类型参数
 *
 * <AUTHOR>
 * @since 2025/08/16 16:14
 */
@Data
@Schema(description = "创建或修改利润类型参数")
public class ProfitTypeReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 媒体平台
     */
    @Schema(description = "媒体平台")
    @NotNull(message = "媒体平台不能为空")
    private Integer adPlatform;

    /**
     * 名称
     */
    @Schema(description = "名称")
    @NotBlank(message = "名称不能为空")
    @Length(max = 64, message = "名称长度不能超过 {max} 个字符")
    private String name;
}