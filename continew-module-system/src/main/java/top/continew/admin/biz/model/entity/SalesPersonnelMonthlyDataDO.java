package top.continew.admin.biz.model.entity;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.starter.extension.crud.model.entity.BaseDO;

/**
 * 商务人员月度绩效数据实体
 *
 * <AUTHOR>
 * @since 2025/08/20 10:43
 */
@Data
@TableName("biz_sales_personnel_monthly_data")
public class SalesPersonnelMonthlyDataDO extends BaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联商务
     */
    private Long businessUserId;

    /**
     * 入职时间
     */
    private LocalDate entryTime;

    /**
     * 岗位类型
     */
    private Integer jobRank;

    /**
     * 统计月份
     */
    private LocalDate salesDate;

    /**
     * 微信好友数量
     */
    private Integer wechatFriendsCount;

    /**
     * Telegram好友数量
     */
    private Integer telegramFriendsCount;

    /**
     * 成交客户数量
     */
    private Integer closedCustomersCount;

    /**
     * 交友数量
     */
    private Integer friendsCount;

    /**
     * 意向客户数量
     */
    private Integer intendedCustomersCount;

    /**
     * 客户总消耗
     */
    private BigDecimal totalCustomerSpend;

    /**
     * 广告户使用率
     */
    private Double adAccountUsageRate;

    /**
     * 客户单户平均消耗
     */
    private BigDecimal avgSingleAccountSpend;

    /**
     * 客户留存率
     */
    private Double customerRetentionRate;

    /**
     * 日报漏填写数量
     */
    private Integer dailyReportMissingCount;

    /**
     * 客咨漏填写数量
     */
    private Integer customerConsultingMissingCount;
}