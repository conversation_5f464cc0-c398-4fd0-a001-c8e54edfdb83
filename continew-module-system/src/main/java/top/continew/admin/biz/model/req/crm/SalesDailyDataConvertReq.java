package top.continew.admin.biz.model.req.crm;

import java.io.Serial;
import java.math.BigDecimal;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 销售日报数据转化请求参数
 *
 * <AUTHOR>
 * @since 2025/01/15
 */
@Data
@Schema(description = "销售日报数据转化请求参数")
public class SalesDailyDataConvertReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 销售日报数据ID
     */
    @Schema(description = "销售日报数据ID")
    @NotNull(message = "销售日报数据ID不能为空")
    private Long recordId;

    /**
     * 转化类型：lead-转化线索，opportunity-转化商机
     */
    @Schema(description = "转化类型：lead-转化线索，opportunity-转化商机")
    @NotBlank(message = "转化类型不能为空")
    private String convertType;

    /**
     * 是否赢单（仅转化商机时使用）
     */
    @Schema(description = "是否赢单（仅转化商机时使用）")
    private Boolean isWon;

    /**
     * 分派商务用户ID（仅转化线索时使用）
     */
    @Schema(description = "分派商务用户ID（仅转化线索时使用）")
    private Long businessUserId;

    /**
     * TG群号（仅转化线索时使用）
     */
    @Schema(description = "TG群号（仅转化线索时使用）")
    private String telegramChatId;


    @Schema(description = "手续费百分比（仅转化商机时使用）")
    private BigDecimal feeRatePercent;

    @Schema(description = "开户费（仅转化商机时使用）")
    private BigDecimal buyAccountFee;
}