/*
 * Copyright (c) 2022-present <PERSON><PERSON>c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import top.continew.starter.core.enums.BaseEnum;

@Getter
@RequiredArgsConstructor
public enum BusinessManagerTypeEnum implements BaseEnum<Integer> {
    BM5(1, "bm5", 1L), BM50(2, "bm50", 2L), BM250(3, "bm250", 3L),
    BM2500(4, "bm2500", 4L), BM10000(5, "bm10000", 5L),
    BM1(7, "BM1", 7L), BILL_ACCOUNT(8, "账单户", 8L),
    BM3(9, "bm3", 9L), BM13000(10, "bm13000", 10L),
    BM3000(11, "bm3000", 11L), BM4000(12, "BM4000", 12L),
    BM5000(13, "BM5000", 13L), BM10(14, "BM10", 14L),
    ;

    private final Integer value;
    private final String description;
    private final Long longValue;
}
