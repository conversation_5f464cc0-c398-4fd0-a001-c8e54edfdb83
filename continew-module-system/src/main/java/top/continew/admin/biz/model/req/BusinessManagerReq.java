/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import top.continew.admin.biz.enums.BusinessManagerBannedReasonEnum;
import top.continew.admin.biz.enums.BusinessManagerStatusEnum;
import top.continew.admin.biz.enums.PersonalAccoutAfterSaleStatusEnum;
import top.continew.starter.extension.crud.model.req.BaseReq;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 创建或修改BM5账号参数
 *
 * <AUTHOR>
 * @since 2024/12/30 17:48
 */
@Data
@Schema(description = "创建或修改BM5账号参数")
public class BusinessManagerReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联渠道
     */
    @Schema(description = "关联渠道")
    @NotNull(message = "关联渠道不能为空")
    private Long channelId;

    /**
     * BM5 ID
     */
    @Schema(description = "BM5 ID")
    @NotBlank(message = "BM5 ID不能为空")
    @Length(max = 64, message = "BM5 ID长度不能超过 {max} 个字符")
    private String platformId;

    /**
     * 浏览器编号
     */
    @Schema(description = "浏览器编号")
    private String browserNo;

    private String remark;

    @NotBlank(message = "账号信息不能为空")
    private String content;


    private Boolean isUse;

    private LocalDateTime useTime;

    private String user;

    private BigDecimal unitPrice;

    private String opsBrowser;
    private String reserveBrowser;
    private String observeBrowser;

    private PersonalAccoutAfterSaleStatusEnum afterSaleStatus;

    private BusinessManagerBannedReasonEnum bannedReason;

    private String afterSaleReason;

    private Long type;

    private Boolean isExternal;

    private Boolean isEnterpriseAuth;

    private Boolean isRemoveAdmin;

    private String reserveBrowserBak;

    private Integer num;

    private Boolean isBu;

    private BusinessManagerStatusEnum status;
}