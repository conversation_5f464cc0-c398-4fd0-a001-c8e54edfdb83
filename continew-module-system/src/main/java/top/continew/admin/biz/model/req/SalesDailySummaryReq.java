package top.continew.admin.biz.model.req;

import java.io.Serial;
import java.time.*;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改商务日报参数
 *
 * <AUTHOR>
 * @since 2025/08/01 14:29
 */
@Data
@Schema(description = "创建或修改商务日报参数")
public class SalesDailySummaryReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 日报记录的日期
     */
    @Schema(description = "日报记录的日期")
    @NotNull(message = "日报记录的日期不能为空")
    private LocalDate recordDate;

    /**
     * 日报具体内容，支持长文本
     */
    @Schema(description = "日报具体内容，支持长文本")
    @NotBlank(message = "日报具体内容，支持长文本不能为空")
    @Length(max = 65535, message = "日报具体内容，支持长文本长度不能超过 {max} 个字符")
    private String content;


    private Long createUser;
}