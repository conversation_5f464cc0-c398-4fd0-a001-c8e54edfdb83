/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import top.continew.admin.biz.enums.CustomerSettleTypeEnum;
import top.continew.admin.biz.enums.CustomerTypeEnum;
import top.continew.admin.biz.enums.RechargeFeeHandleMethodEnum;
import top.continew.starter.extension.crud.model.req.BaseReq;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 创建或修改客户参数
 *
 * <AUTHOR>
 * @since 2024/12/30 17:56
 */
@Data
@Schema(description = "创建或修改客户参数")
public class CustomerReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    @Schema(description = "名称")
    @NotBlank(message = "名称不能为空")
    @Length(max = 64, message = "名称长度不能超过 {max} 个字符")
    private String name;

    /**
     * 关联商务
     */
    @Schema(description = "关联商务")
    @NotNull(message = "关联商务不能为空")
    private Long businessUserId;

    private Long agentId;

    private String rebateRule;

    /**
     * 手续费百分比
     */
    @Schema(description = "手续费百分比")
    @NotNull(message = "手续费百分比不能为空")
    private BigDecimal feeRatePercent;

    /**
     * 手续费扣款方式
     */
    @Schema(description = "手续费扣款方式")
    private RechargeFeeHandleMethodEnum feeHandleMethod;

    /**
     * TG群ID
     */
    @Schema(description = "TG群ID")
    private String telegramChatId;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    @Schema(description = "机器人权限")
    private String robotPermission;

    private BigDecimal buyAccountFee;

    private Boolean isRefund;


    /**
     * 结算方式
     */
    private CustomerSettleTypeEnum settleType;

    /**
     * 结算限额
     */
    private BigDecimal settleLimitAmount;

    /**
     * 预警限额
     */
    private BigDecimal warnLimitAmount;


    /**
     * 客户类型：1是正式客户，2是潜在客户
     */
    private CustomerTypeEnum type;

    /**
     * 客户来源ID
     */
    private Long sourceId;

    /**
     * 客户行业
     */
    private Integer industry;


    @Schema(description = "公司名称")
    private String companyName;
    @Schema(description = "客户职位")
    private String customerPosition;
    @Schema(description = "城市")
    private String city;
    @Schema(description = "团队规模")
    private String teamSize;
    @Schema(description = "团队单日消耗(美元)")
    private String dailyTeamSpending;
    @Schema(description = "产品名称")
    private String productName;

    private Boolean debtNotify;

    private Integer businessType;

    private String dataTemplate;

    private String effectTemplate;

}