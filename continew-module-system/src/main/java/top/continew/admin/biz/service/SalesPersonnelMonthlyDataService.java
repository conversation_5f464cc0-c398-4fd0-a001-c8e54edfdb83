package top.continew.admin.biz.service;

import top.continew.admin.biz.model.entity.SalesPersonnelMonthlyDataDO;
import top.continew.admin.system.enums.JobRankEnum;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.service.BaseService;
import top.continew.admin.biz.model.query.SalesPersonnelMonthlyDataQuery;
import top.continew.admin.biz.model.req.SalesPersonnelMonthlyDataReq;
import top.continew.admin.biz.model.resp.SalesPersonnelMonthlyDataDetailResp;
import top.continew.admin.biz.model.resp.SalesPersonnelMonthlyDataResp;

import java.util.List;

/**
 * 商务人员月度绩效数据业务接口
 *
 * <AUTHOR>
 * @since 2025/08/20 10:43
 */
public interface SalesPersonnelMonthlyDataService extends BaseService<SalesPersonnelMonthlyDataResp, SalesPersonnelMonthlyDataDetailResp, SalesPersonnelMonthlyDataQuery, SalesPersonnelMonthlyDataReq>, IService<SalesPersonnelMonthlyDataDO> {

    List<SalesPersonnelMonthlyDataDO> listByUserIds(List<Long> userIds, JobRankEnum jobRank);

}