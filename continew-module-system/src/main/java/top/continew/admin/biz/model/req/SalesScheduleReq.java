package top.continew.admin.biz.model.req;

import java.io.Serial;
import java.time.*;


import jakarta.validation.constraints.NotNull;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.admin.biz.enums.ScheduleTypeEnum;
import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改商务人员排班/请假记录参数
 *
 * <AUTHOR>
 * @since 2025/08/04 11:56
 */
@Data
@Schema(description = "创建或修改商务人员排班/请假记录参数")
public class SalesScheduleReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;


    /**
     * 商务人员ID
     */
    @Schema(description = "商务人员ID")
    @NotNull(message = "商务人员ID不能为空")
    private Long salesId;

    /**
     * 排班/请假的具体日期
     */
    @Schema(description = "排班/请假的具体日期")
    @NotNull(message = "排班/请假的具体日期不能为空")
    private LocalDate scheduleDate;

    /**
     * 类型: 1=请假, 2=排班
     */
    @Schema(description = "类型: 1=请假, 2=排班")
    @NotNull(message = "类型: 1=请假, 2=排班不能为空")
    private ScheduleTypeEnum scheduleType;

}