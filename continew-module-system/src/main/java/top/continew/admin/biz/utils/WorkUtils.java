package top.continew.admin.biz.utils;

import cn.hutool.core.date.Month;
import cn.hutool.core.io.resource.ResourceUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.HashMap;
import java.util.Map;

/**
 * 工作日计算工具类
 *
 * <AUTHOR> 4.0 sonnet
 * @since 2025/08/07
 */
@Component
@Slf4j
public class WorkUtils {

    /**
     * 节假日数据缓存
     * key: 日期, value: 是否为休息日(true=休息日, false=工作日/调休)
     */
    private static final Map<LocalDate, Boolean> holidayMap = new HashMap<>();

    static {
        loadHolidayData();
    }

    /**
     * 加载节假日数据
     */
    private static void loadHolidayData() {
        try {
            JSONArray holidayArray = JSONArray.parseArray(ResourceUtil.readUtf8Str("data/2025.json"));

            for (int i = 0; i < holidayArray.size(); i++) {
                JSONObject holiday = holidayArray.getJSONObject(i);
                String dateStr = holiday.getString("date");
                Boolean isOffDay = holiday.getBoolean("isOffDay");

                if (dateStr != null && isOffDay != null) {
                    LocalDate date = LocalDate.parse(dateStr);
                    holidayMap.put(date, isOffDay);
                }
            }

            log.info("成功加载节假日数据，共 {} 条记录", holidayMap.size());

        } catch (Exception e) {
            log.error("加载节假日数据失败", e);
        }
    }

    /**
     * 计算指定月份的工作日数量
     * 工作日定义：周一到周五，排除法定节假日，包含调休工作日
     *
     * @param month 指定月份
     * @return 工作日数量
     */
    public static int getWorkingDaysInMonth(YearMonth month) {
        if (month == null) {
            return 0;
        }

        int workingDays = 0;
        LocalDate startDate = month.atDay(1);
        LocalDate endDate = month.atEndOfMonth();

        // 如果是当前月份，只计算到今天
        LocalDate today = LocalDate.now();
        if (month.equals(YearMonth.now())) {
            endDate = today;
        }

        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            if (isWorkingDay(currentDate)) {
                workingDays++;
            }
            currentDate = currentDate.plusDays(1);
        }

        return workingDays;
    }

    /**
     * 判断指定日期是否为工作日
     *
     * @param date 指定日期
     * @return true=工作日, false=休息日
     */
    private static boolean isWorkingDay(LocalDate date) {
        if (date == null) {
            return false;
        }

        // 检查是否在节假日数据中
//        Boolean isOffDay = holidayMap.get(date);
//        if (isOffDay != null) {
//            // 如果在节假日数据中，直接返回相反值
//            // isOffDay=true表示休息日，isOffDay=false表示调休工作日
//            return !isOffDay;
//        }

        // 如果不在节假日数据中，按正常逻辑判断
        // 周一到周五为工作日，周末为休息日
        DayOfWeek dayOfWeek = date.getDayOfWeek();
//        return dayOfWeek != DayOfWeek.SATURDAY && dayOfWeek != DayOfWeek.SUNDAY;
        return dayOfWeek != DayOfWeek.SUNDAY;
    }

    /**
     * 判断指定日期是否为工作日（周末）
     *
     * @param date 指定日期
     * @return true=工作日, false=休息日
     */
    private static boolean isSunday(LocalDate date) {
        if (date == null) {
            return false;
        }

        DayOfWeek dayOfWeek = date.getDayOfWeek();
        return  dayOfWeek == DayOfWeek.SUNDAY;
    }



    /**
     * 判断指定日期是否为工作日（公开方法，供外部调用）
     *
     * @param date 指定日期
     * @return true=工作日, false=休息日
     */
    public static boolean isWorkDay(LocalDate date) {
        return isWorkingDay(date);
    }

    /**
     * 计算指定月份到今天为止的工作日数量
     *
     * @param month 指定月份
     * @return 工作日数量
     */
    public static int getWorkingDaysInMonthUntilToday(YearMonth month) {
        if (month == null) {
            return 0;
        }

        int workingDays = 0;
        LocalDate startDate = month.atDay(1);
        LocalDate endDate = month.atEndOfMonth();
        LocalDate today = LocalDate.now();

        // 确定结束日期：月末或今天，取较早的那个
        if (today.isBefore(endDate)) {
            endDate = today;
        }

        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            if (isWorkingDay(currentDate)) {
                workingDays++;
            }
            currentDate = currentDate.plusDays(1);
        }

        return workingDays;
    }

    /**
     * 计算指定时间范围内的工作日数量
     *
     * @param dateTimeRange 时间范围数组，[0]为开始时间，[1]为结束时间
     * @return 工作日数量
     */
    public static int getWorkingDaysInRange(LocalDateTime[] dateTimeRange) {
        if (dateTimeRange == null || dateTimeRange.length != 2) {
            return 0;
        }

        LocalDateTime startDateTime = dateTimeRange[0];
        LocalDateTime endDateTime = dateTimeRange[1];

        if (startDateTime == null || endDateTime == null) {
            return 0;
        }

        if (startDateTime.isAfter(endDateTime)) {
            return 0;
        }

        // 转换为日期进行计算（忽略时分秒）
        LocalDate startDate = startDateTime.toLocalDate();
        LocalDate endDate = endDateTime.toLocalDate();

        return getWorkingDaysInRange(startDate, endDate);
    }

    /**
     * 计算指定日期范围内的工作日数量
     *
     * @param startDate 开始日期（包含）
     * @param endDate 结束日期（包含）
     * @return 工作日数量
     */
    public static int getWorkingDaysInRange(LocalDate startDate, LocalDate endDate) {
        if (startDate == null || endDate == null) {
            return 0;
        }

        if (startDate.isAfter(endDate)) {
            return 0;
        }

        int workingDays = 0;
        LocalDate currentDate = startDate;

        while (!currentDate.isAfter(endDate)) {
            if (!isSunday(currentDate)) {
                workingDays++;
            }
            currentDate = currentDate.plusDays(1);
        }
        return workingDays;
    }

    /**
     * 判断两个日期是否为同一个月份的完整范围
     * 例如：2025-01-01 和 2025-01-31 返回 true
     *      2025-01-01 和 2025-01-30 返回 false
     *      2025-02-01 和 2025-02-28 返回 true（非闰年）
     *      2024-02-01 和 2024-02-29 返回 true（闰年）
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return true=是同一个月份的完整范围, false=不是
     */
    public static boolean isFullMonthRange(LocalDate startDate, LocalDate endDate) {
        if (startDate == null || endDate == null) {
            return false;
        }

        // 检查开始日期是否为月初（第1天）
        if (startDate.getDayOfMonth() != 1) {
            return false;
        }

        // 检查两个日期是否在同一年同一月
        if (startDate.getYear() != endDate.getYear() ||
            startDate.getMonthValue() != endDate.getMonthValue()) {
            return false;
        }

        // 获取该月的最后一天
        YearMonth yearMonth = YearMonth.of(startDate.getYear(), startDate.getMonth());
        LocalDate lastDayOfMonth = yearMonth.atEndOfMonth();

        // 检查结束日期是否为该月的最后一天
        return endDate.equals(lastDayOfMonth);
    }
}
