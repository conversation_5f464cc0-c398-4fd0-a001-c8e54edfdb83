package top.continew.admin.biz.model.req;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改投放日报明细参数
 *
 * <AUTHOR>
 * @since 2025/08/13 21:02
 */
@Data
@Schema(description = "创建或修改投放日报明细参数")
public class AdProductStatItemReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 广告户
     */
    @Schema(description = "广告户")
    @NotBlank(message = "广告户不能为空")
    @Length(max = 64, message = "广告户长度不能超过 {max} 个字符")
    private String platformAdId;

    /**
     * 消耗
     */
    @Schema(description = "消耗")
    @NotNull(message = "消耗不能为空")
    @Min(value = 0, message = "消耗必须大于等于0")
    private BigDecimal spend;

    /**
     * 回流
     */
    @Schema(description = "回流")
    @NotNull(message = "回流不能为空")
    @Min(value = 0, message = "回流必须大于等于0")
    private BigDecimal reflowSpend;

    /**
     * 成效数据
     */
    @Schema(description = "成效数据")
    @Length(max = 1024, message = "成效数据长度不能超过 {max} 个字符")
    private String effectData;

    @NotNull(message = "统计记录不能为空")
    private Long statId;
}