/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.biz.enums.*;
import top.continew.admin.common.base.BaseResp;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 广告账号信息
 *
 * <AUTHOR>
 * @since 2024/12/30 17:50
 */
@Data
@Schema(description = "广告账号信息")
public class AdAccountResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联BM5 ID
     */
    @Schema(description = "关联BM5 ID")
    private Long businessManagerId;


    private Long bmType;

    /**
     * 平台用户ID
     */
    @Schema(description = "平台用户ID")
    private String platformAccountId;

    /**
     * 平台广告ID
     */
    @Schema(description = "平台广告ID")
    private String platformAdId;

    /**
     * 完整卡号
     */
    @Schema(description = "完整卡号")
    private String fullCardNumber;

    /**
     * 时区
     */
    @Schema(description = "时区")
    private String timezone;

    /**
     * 账号状态
     */
    @Schema(description = "账号状态")
    private AdAccountStatusEnum accountStatus;

    /**
     * 养号状态
     */
    @Schema(description = "养号状态")
    private AdAccountKeepStatusEnum keepStatus;

    /**
     * 出售状态
     */
    @Schema(description = "出售状态")
    private AdAccountSaleStatusEnum saleStatus;


    /**
     * 是否可用
     */
    private Boolean usable;

    /**
     * 不可用原因
     */
    private AdAccountUnusableReasonEnum unusableReason;

    /**
     * 个号地区
     */
    @Schema(description = "个号地区")
    private String personalArea;

    /**
     * 浏览器编号
     */
    @Schema(description = "浏览器编号")
    private String browserNo;

    /**
     * 清零状态
     */
    @Schema(description = "清零状态")
    private AdAccountClearStatusEnum clearStatus;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 花费限额
     */
    private BigDecimal spendCap;

    /**
     * 消耗
     */
    private BigDecimal amountSpent;

    /**
     * 剩余应付
     */
    private BigDecimal balance;

    /**
     * 卡数
     */
    private Integer cardCount;

    private String bmId;

    private LocalDateTime updateTime;

    private String tag;

    /**
     * 总花费
     */
    private BigDecimal totalSpent;

    private String name;

    private String billCountry;

    private String billCurrency;

    /**
     * 申诉状态
     */
    private AdAccountAppealStatusEnum appealStatus;

    private LocalDateTime bmAuthTime;

    private LocalDateTime banTime;

    private String bmBrowser;


    /**
     * 绑定的客户名称
     */
    private String customerName;

    /**
     * 绑定的下户日期
     */
    private LocalDateTime payTime;

    private LocalDateTime saleTime;

    private BigDecimal cost;

    private String bm1Browser;

    private String parentBrowserNo;

    private String bm1Id;

    private String bm1PlatformId;

    private Boolean isRemoveAdmin;

    private Boolean isLowLimit;

    private BigDecimal realAdtrustDsl;

    private Long bmItemType;

    private String typeName;

    private Integer voStatus;

    private Integer testOrderCount;


    private String tags;

    private String channelName;

    private String headers;

    private BigDecimal prepayAccountBalance;

    private Boolean isPrepay;

    private String disabledReason;

}