/*
 * Copyright (c) 2022-present <PERSON><PERSON><PERSON> Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import top.continew.starter.core.enums.BaseEnum;

@Getter
@RequiredArgsConstructor
public enum MaterialTypeEnum implements BaseEnum<Integer> {
    BM5(1, "BM5", 1L), BIG_BLACK_ACCOUNT(2, "大黑号", 747130507820531714L),
    SECOND_SOLUTION_ACCOUNT(3, "二解号", 747144506897334273L), THREE_SOLUTION_ACCOUNT(4, "三解号", 747487156104069123L),
    SHORT_ID_ACCOUNT(5, "短链号", 747493385660530741L), OLD_AMERICAN_ACCOUNT(6, "美国老号", 747493385660530741L),
    OUTLOOK_EMAIL(8, "outlook邮箱", 747493385660530741L), BM(9, "BM", 747493385660530741L),
    PUBLIC_HOME_PAGE(10, "公共主页", 747489335380541473L), SMS_PLATFORM(11, "接码平台充值", 747490309700583461L),
    TRX_BUY(12, "购买TRX", 747490490709966887L), PROXY(13, "代理IP", 747490518895689769L),
    ADS_POWER(14, "Adspower费用", 747490555205779499L), BILL_ACCOUNT(15, "账单户", 8L),
    CLOUD_SERVER(16, "FB服务器", 747491013710315567L), CHROME_PLUGIN(17, "越南插件", 747491084728270897L),
    BM_2500(18, "BM2500分享户", 4L), BM250(19, "BM250", 3L), BM10000(20, "BM10000分享户", 5L), BM1(21, "BM1", 7L),
    ACCOUNT_WITH_COMPANY_AUTH(22, "企业认证户", 747488281653936135L), BM13000(23, "BM13000分享户", 10L),
    BM3000(24, "BM3000分享户", 11L), BM3(25, "BM3", 9L), BM10(26, "BM10", 14L), BM50(27, "BM50", 2L),
    BM4000(28, "BM4000", 12L), BM5000(29, "BM5000", 13L), BM1_ENTERPRISE_AUTH(30, "企业认证BM1", 747488281653936135L),
    CLONE_BROWSER(80, "ClonBrowser费用", 747490555205779499L), GOOGLE_AD_ACCOUNT(81, "GOOGLE账号", 747783012686497232L),
    OTHER(99, "其他", 747493385660530741L);

    private final Integer value;
    private final String description;
    private final Long longValue;
}
