package top.continew.admin.biz.model.req;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.admin.biz.enums.AdProjectEnum;
import top.continew.admin.biz.enums.TransactionUserTypeEnum;
import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改利润参数
 *
 * <AUTHOR>
 * @since 2025/08/16 16:14
 */
@Data
@Schema(description = "创建或修改利润参数")
public class ProfitReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 媒体平台
     */
    @Schema(description = "媒体平台")
    @NotNull(message = "媒体平台不能为空")
    private Integer adPlatform;

    /**
     * 项目
     */
    @Schema(description = "项目")
    @NotNull(message = "项目不能为空")
    private AdProjectEnum project;

    /**
     * 类型
     */
    @Schema(description = "类型")
    @NotNull(message = "类型不能为空")
    private Long type;

    /**
     * 交易金额
     */
    @Schema(description = "交易金额")
    @NotNull(message = "交易金额不能为空")
    private BigDecimal amount;

    /**
     * 交易时间
     */
    @Schema(description = "交易时间")
    @NotNull(message = "交易时间不能为空")
    private LocalDateTime transTime;

    /**
     * 交易哈希
     */
    @Schema(description = "交易哈希")
    @Length(max = 64, message = "交易哈希长度不能超过 {max} 个字符")
    private String transactionHash;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Length(max = 255, message = "备注长度不能超过 {max} 个字符")
    private String remark;

    @NotNull(message = "交易对象不能为空")
    private Long transactionUserId;

    @NotNull(message = "交易对象类型不能为空")
    private TransactionUserTypeEnum transactionUserType;

    @NotNull(message = "交易数量不能为空")
    @Min(value = 1, message = "交易数量不能小于1")
    private Integer num;
}