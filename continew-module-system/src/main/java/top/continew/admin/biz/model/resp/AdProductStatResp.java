package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.biz.enums.AdProductTypeEnum;
import top.continew.admin.common.base.BaseResp;

/**
 * 产品日报信息
 *
 * <AUTHOR>
 * @since 2025/08/12 15:07
 */
@Data
@Schema(description = "产品日报信息")
public class AdProductStatResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户
     */
    @Schema(description = "客户")
    private Long customerId;

    private String customerName;

    /**
     * 产品
     */
    @Schema(description = "产品")
    private Long productId;

    private String productName;

    private AdProductTypeEnum productType;

    /**
     * 统计日期
     */
    @Schema(description = "统计日期")
    private LocalDate statDate;

    /**
     * 广告户
     */
    @Schema(description = "广告户")
    private String platformAdId;

    /**
     * 实际花费
     */
    @Schema(description = "实际花费")
    private BigDecimal actualSpend;

    /**
     * 花费
     */
    @Schema(description = "花费")
    private BigDecimal spend;

    /**
     * 服务费
     */
    @Schema(description = "服务费")
    private BigDecimal fee;

    /**
     * 额外数据
     */
    @Schema(description = "额外数据")
    private String extraData;

    private String effectData;

    private BigDecimal feeRate;

    private BigDecimal reflowSpend;

    private BigDecimal totalSpend;

    private BigDecimal totalCost;

    private BigDecimal bearCost;
}