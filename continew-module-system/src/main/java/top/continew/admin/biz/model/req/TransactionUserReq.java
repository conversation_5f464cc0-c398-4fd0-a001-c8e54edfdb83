package top.continew.admin.biz.model.req;

import java.io.Serial;
import java.time.*;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.admin.biz.enums.TransactionUserTypeEnum;
import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改交易对象参数
 *
 * <AUTHOR>
 * @since 2025/08/17 14:22
 */
@Data
@Schema(description = "创建或修改交易对象参数")
public class TransactionUserReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 交易类型
     */
    @Schema(description = "交易类型")
    @NotNull(message = "交易类型不能为空")
    private TransactionUserTypeEnum transactionType;

    /**
     * 关联ID
     */
    @Schema(description = "关联ID")
    private Long referId;

    /**
     * 名字
     */
    @Schema(description = "名字")
    @NotBlank(message = "名字不能为空")
    @Length(max = 64, message = "名字长度不能超过 {max} 个字符")
    private String name;
}