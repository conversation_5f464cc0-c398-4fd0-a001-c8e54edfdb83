package top.continew.admin.biz.service.impl.crm;

import cn.crane4j.core.support.OperateTemplate;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import top.continew.admin.biz.mapper.crm.SalesPerformanceStatMapper;
import top.continew.admin.biz.model.entity.SalesPersonnelMonthlyDataDO;
import top.continew.admin.biz.model.query.PerformanceStatisticsQuery;
import top.continew.admin.biz.model.query.crm.SalesPerformanceStatQuery;
import top.continew.admin.biz.model.resp.BusinessPerformanceStatisticsResp;
import top.continew.admin.biz.model.resp.SalesDataSummaryResp;
import top.continew.admin.biz.model.resp.SalesPersonnelMonthlyDataResp;
import top.continew.admin.biz.model.resp.crm.SalesPerformanceStatResp;
import top.continew.admin.biz.service.SalesPersonnelMonthlyDataService;
import top.continew.admin.biz.service.SalesScheduleService;
import top.continew.admin.biz.service.crm.SalesPerformanceStatService;
import top.continew.admin.biz.utils.CommonUtils;
import top.continew.admin.biz.utils.WorkUtils;
import top.continew.admin.system.enums.JobRankEnum;
import top.continew.admin.system.model.entity.UserDO;
import top.continew.admin.system.service.UserService;
import top.continew.starter.core.exception.BusinessException;
import top.continew.starter.core.validation.CheckUtils;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 商务业绩统计 Service 实现
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SalesPerformanceStatServiceImpl implements SalesPerformanceStatService {

    private final SalesPerformanceStatMapper salesPerformanceStatMapper;

    private final UserService userService;

    private final SalesPersonnelMonthlyDataService salesPersonnelMonthlyDataService;


    @Override
    public List<SalesPerformanceStatResp> listSalesPerformanceStat(SalesPerformanceStatQuery query) {
        return salesPerformanceStatMapper.selectSalesPerformanceStat(
                query.getSalesUserIds(),
                query.getStartDate(),
                query.getEndDate()
        );
    }

    @Override
    public List<SalesPersonnelMonthlyDataResp> listBusinessPerformanceStat(PerformanceStatisticsQuery query) {
        // 参数验证
        if (query == null || query.getJobRank() == null) {
            throw new BusinessException("查询参数或职级不能为空");
        }

        // 对应职级的用户
        List<UserDO> userList = userService.lambdaQuery()
                .eq(UserDO::getDeptId, 702164640200656226L)
                .in(CollUtil.isNotEmpty(query.getSalesIds()), UserDO::getId, query.getSalesIds())
                .eq(null != query.getJobRank(), UserDO::getJobRank, query.getJobRank())
                .list();

        if (userList.isEmpty()) {
            return List.of();
        }

        List<Long> ids = userList.stream().map(BaseIdDO::getId).toList();

        // 根据职级分支处理
        return switch (query.getJobRank()) {
            case PROBATION -> calculateProbationStat(ids);
            case TRIAL -> calculateTrialStat(ids, query);
            case FORMAL -> calculateFormalStat(ids, query);
        };
    }

    @Override
    public SalesDataSummaryResp getSalesDataSummary(LocalDateTime startTime, LocalDateTime endTime) {
        return salesPerformanceStatMapper.selectSalesDataSummary(startTime, endTime);
    }

    /**
     * 计算试岗期业绩统计
     * 试岗期关注：微信好友数量、Telegram好友数量、客咨漏填写数量
     */
    private List<SalesPersonnelMonthlyDataResp> calculateProbationStat(List<Long> ids) {
        List<SalesPersonnelMonthlyDataDO> existingData = salesPersonnelMonthlyDataService.listByUserIds(ids, JobRankEnum.PROBATION);

        List<SalesPersonnelMonthlyDataResp> result = new ArrayList<>();

        // 如果没有现有数据，直接从统计表获取所有数据
        if (CollUtil.isEmpty(existingData)) {
            List<BusinessPerformanceStatisticsResp> stats = salesPerformanceStatMapper.selectProbationStat(ids);
            for (BusinessPerformanceStatisticsResp stat : stats) {
                result.add(convertToResponse(stat));
            }
        } else {
            // 转换现有数据
            for (SalesPersonnelMonthlyDataDO data : existingData) {
                SalesPersonnelMonthlyDataResp resp = new SalesPersonnelMonthlyDataResp();
                BeanUtil.copyProperties(data, resp);
                result.add(resp);
            }

            // 获取缺失用户的统计数据
            List<Long> existingUserIds = existingData.stream()
                    .map(SalesPersonnelMonthlyDataDO::getBusinessUserId)
                    .toList();
            List<Long> missingUserIds = (List<Long>) CollUtil.subtract(ids, existingUserIds);

            if (CollUtil.isNotEmpty(missingUserIds)) {
                List<BusinessPerformanceStatisticsResp> missingStats = salesPerformanceStatMapper.selectProbationStat(missingUserIds);
                for (BusinessPerformanceStatisticsResp stat : missingStats) {
                    result.add(convertToResponse(stat));
                }
            }
        }

        // 按 businessName 排序（null值排在最后）
        result.sort(Comparator.comparing(SalesPersonnelMonthlyDataResp::getBusinessName,
                                       Comparator.nullsLast(Comparator.naturalOrder())));
        OperateTemplate operateTemplate = SpringUtil.getBean(OperateTemplate.class);
        operateTemplate.execute(result);
        return result;
    }



    /**
     * 计算试用期业绩统计
     * 试用期关注：成交客户数、意向客户数、交友数、客户总消耗、日报漏填写数量、客咨漏填写数量
     */
    private List<SalesPersonnelMonthlyDataResp> calculateTrialStat(List<Long> ids, PerformanceStatisticsQuery query) {
        CheckUtils.throwIf(query.getStartDate() == null || query.getEndDate() == null, "开始时间和结束时间不能为空");

        

        int workingDays = WorkUtils.getWorkingDaysInRange(query.getStartDate().toLocalDate(), query.getEndDate().toLocalDate());


        // return salesPerformanceStatMapper.selectTrialStat(ids, query.getStartDate(), query.getEndDate(), workingDays);
        return List.of();
    }

    /**
     * 计算正式期业绩统计
     * 正式期关注：新成交客户数、广告户使用率、客户单户平均消耗、客户留存率、交友数、客户总消耗、日报漏填写数量、客咨漏填写数量
     */
    private List<SalesPersonnelMonthlyDataResp> calculateFormalStat(List<Long> ids, PerformanceStatisticsQuery query) {
        CheckUtils.throwIf(query.getStartDate() == null || query.getEndDate() == null, "开始时间和结束时间不能为空");
        int workingDays = WorkUtils.getWorkingDaysInRange(query.getStartDate().toLocalDate(), query.getEndDate().toLocalDate());

        // return salesPerformanceStatMapper.selectFormalStat(ids, query.getStartDate(), query.getEndDate(), workingDays);
        return List.of();
    }


    /**
     * 转换 BusinessPerformanceStatisticsResp 到 SalesPersonnelMonthlyDataResp
     */
    private SalesPersonnelMonthlyDataResp convertToResponse(BusinessPerformanceStatisticsResp source) {
        SalesPersonnelMonthlyDataResp target = new SalesPersonnelMonthlyDataResp();
        BeanUtil.copyProperties(source, target);
        return target;
    }
}