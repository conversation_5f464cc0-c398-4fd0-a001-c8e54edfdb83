package top.continew.admin.biz.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import top.continew.starter.extension.crud.model.req.BaseReq;

import java.io.Serial;
import java.util.List;

/**
 * 创建或修改投放产品参数
 *
 * <AUTHOR>
 * @since 2025/08/12 15:07
 */
@Data
@Schema(description = "创建或修改投放产品参数")
public class AdProductReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户
     */
    @Schema(description = "客户")
    @NotNull(message = "客户不能为空")
    private Long customerId;

    /**
     * 代理线
     */
    @Schema(description = "代理线")
    @NotBlank(message = "代理线不能为空")
    @Length(max = 64, message = "代理线长度不能超过 {max} 个字符")
    private String agentNo;

    /**
     * 产品类型
     */
    @Schema(description = "产品类型")
    @NotNull(message = "产品类型不能为空")
    private Integer type;

    private String country;
}