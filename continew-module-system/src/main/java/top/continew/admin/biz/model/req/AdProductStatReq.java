package top.continew.admin.biz.model.req;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改产品日报参数
 *
 * <AUTHOR>
 * @since 2025/08/12 15:07
 */
@Data
@Schema(description = "创建或修改产品日报参数")
public class AdProductStatReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户
     */
    @Schema(description = "客户")
    @NotNull(message = "客户不能为空")
    private Long customerId;

    /**
     * 产品
     */
    @Schema(description = "产品")
    @NotNull(message = "产品不能为空")
    private Long productId;

    /**
     * 统计日期
     */
    @Schema(description = "统计日期")
    @NotNull(message = "统计日期不能为空")
    private LocalDate statDate;
    /**
     * 花费
     */
    @Schema(description = "花费")
    @NotNull(message = "花费不能为空")
    private BigDecimal spend;

    private BigDecimal feeRate;

    private BigDecimal fee;

    @NotNull(message = "回流不能为空")
    private BigDecimal reflowSpend;

    private String extraData;


    private BigDecimal bearCost;
}