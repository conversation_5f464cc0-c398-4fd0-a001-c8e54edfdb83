/*
 * Copyright (c) 2022-present <PERSON>7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import top.continew.starter.core.enums.BaseEnum;

@Getter
@RequiredArgsConstructor
public enum TransactionUserTypeEnum implements BaseEnum<Integer> {
    AD_ACCOUNT_CUSTOMER(1, "卖户客户"), MATERIAL_CHANNEL(2, "物料渠道"), AD_ACCOUNT_AGENT(3, "卖户中介"), TOULIU_CUSTOMER(4, "投流甲方"),
    TOULIU_TEAM(5, "代投团队"), MATERIAL_SHOP_USER(6, "物料商城用户");

    private final Integer value;
    private final String description;
}