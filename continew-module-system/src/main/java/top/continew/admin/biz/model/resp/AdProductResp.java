package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.common.base.BaseResp;

/**
 * 投放产品信息
 *
 * <AUTHOR>
 * @since 2025/08/12 15:07
 */
@Data
@Schema(description = "投放产品信息")
public class AdProductResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户
     */
    @Schema(description = "客户")
    private Long customerId;

    /**
     * 代理线
     */
    @Schema(description = "代理线")
    private String agentNo;

    /**
     * 产品类型
     */
    @Schema(description = "产品类型")
    private Integer type;

    private String customerName;

    private String country;
}