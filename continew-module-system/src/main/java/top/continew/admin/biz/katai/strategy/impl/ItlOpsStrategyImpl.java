/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.katai.strategy.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.enums.*;
import top.continew.admin.biz.katai.strategy.CardOpsStrategy;
import top.continew.admin.biz.model.entity.CardBalanceDO;
import top.continew.admin.biz.model.entity.CardDO;
import top.continew.admin.biz.model.entity.CardTransactionDO;
import top.continew.katai.ItlClient;
import top.continew.katai.exception.ThirdException;
import top.continew.katai.gzy.model.req.GzyGetTransactionRequest;
import top.continew.katai.gzy.model.req.GzyShareCardTxnLimitRequest;
import top.continew.katai.gzy.model.resp.*;
import top.continew.katai.itl.ItlConfig;
import top.continew.katai.itl.model.req.ItlCardOpenReq;
import top.continew.katai.itl.model.req.ItlCreateCardholderReq;
import top.continew.katai.itl.model.req.ItlEnableCardReq;
import top.continew.katai.itl.model.req.ItlFreezeCardReq;
import top.continew.katai.itl.model.req.ItlUpdateCardLimitReq;
import top.continew.katai.itl.model.resp.*;
import top.continew.starter.cache.redisson.util.RedisUtils;
import top.continew.starter.extension.crud.model.resp.LabelValueResp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Interlace卡台策略实现类
 *
 * <AUTHOR>
 * @since 2025/3/13
 */
@Slf4j
@Service
public class ItlOpsStrategyImpl implements CardOpsStrategy {

    private static final String ITL_TOKEN_KEY_PREFIX = "katai:itl:token:";
    private String itlTokenKey;
    public static final int DEFAULT_PAGE_SIZE = 100;
    private volatile ItlClient client;
    private final Object lock = new Object();
    private static final long TOKEN_EXPIRE_TIME = 3600; // 1小时

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    public static final String ITL_3DS_CODE_KEY = "itl:3ds:otp";


    @PostConstruct
    public void init() {
        prodInit();
    }

    /**
     * 生产的配置
     */
    private void testInit() {
        ItlConfig config = new ItlConfig();
        config.setClientId("interlacef5df9455bc7a4656");
        config.setClientSecret("884d6ca143934336805c6e9f4b3e211a");
        config.setEndpoint("api-sandbox.interlace.money");
        client = new ItlClient(config);
        // 设置生产环境的 Redis key
        itlTokenKey = ITL_TOKEN_KEY_PREFIX + "test";
    }


    private void prodInit() {
        ItlConfig config = new ItlConfig();
        config.setClientId("interlace8d096032e44f1124");
        config.setClientSecret("c146507d01b140eea50383ccb68f964e");
        config.setEndpoint("api.interlace.money");
        client = new ItlClient(config);
        // 设置生产环境的 Redis key
        itlTokenKey = ITL_TOKEN_KEY_PREFIX + "prod";
    }

    @Override
    public CardPlatformEnum getCardPlatform() {
        return CardPlatformEnum.INTERLACE;
    }

    @Override
    public List<CardDO> getCardList(LocalDateTime start, LocalDateTime end, Integer syncPage) {
        log.info("======【{}】开始获取卡片列表======", getCardPlatform().getDescription());
        List<CardDO> cardList = new ArrayList<>();

        try {
            //0表示第一页
            int page = 0;
            int totalPages = 0;
            
            while (page <= totalPages) {
                if (syncPage != null && page > (syncPage - 1)) {
                    break;
                }

                log.info("【{}】开始同步第{}页数据...", getCardPlatform().getDescription(), page);

                ItlCardListResp resp = getClient().getCards(DEFAULT_PAGE_SIZE, page);
                if (resp != null && resp.getData() != null) {
                    totalPages = resp.getPageTotal();
                    for (ItlCardDetailResp item : resp.getData()) {
                        cardList.add(convertCard(item));
                    }
                }
                page++;
            }
        } catch (ThirdException e) {
            log.error("获取卡片列表失败", e);
            if (e.getMessage().contains("token") || e.getMessage().contains("Token")) {
                // token失效，清除缓存
                redisTemplate.delete(itlTokenKey);
                // 重试一次
                return getCardList(start, end, syncPage);
            }
        }
        
        log.info("======【{}】卡片列表获取完成，共{}张卡片======", getCardPlatform().getDescription(), cardList.size());
        return cardList;
    }

    private CardDO convertCard(ItlCardDetailResp item) {
        CardDO cardDO = new CardDO();
        cardDO.setPlatform(getCardPlatform());
        cardDO.setCardNumber(item.getBin() + "******" + item.getLast4());
        cardDO.setPlatformCardId(item.getId());
        if(null != item.getBalance() && null != item.getBalance().getAvailable()){
            cardDO.setBalance(new BigDecimal(item.getBalance().getAvailable()));
        }else {
            cardDO.setBalance(BigDecimal.ZERO);
        }

        if(null != item.getStatistics() && null != item.getStatistics().getNetConsumption()) {
            cardDO.setUsedAmount(new BigDecimal(item.getStatistics().getNetConsumption()));
        }else {
            cardDO.setUsedAmount(BigDecimal.ZERO);
        }


        cardDO.setAssociation(item.getNetwork());
        cardDO.setRemark(item.getLabel());

        // 设置卡片状态
        if (StrUtil.isNotBlank(item.getStatus())) {
            switch (item.getStatus()) {
                case "Active":
                    cardDO.setStatus(CardStatusEnum.NORMAL);
                    break;
                case "Pending":
                    cardDO.setStatus(CardStatusEnum.PENDING);
                    break;
                case "LOCKED":
                    cardDO.setStatus(CardStatusEnum.LOCKED);
                    break;
                case "Frozen":
                    cardDO.setStatus(CardStatusEnum.FROZEN);
                    break;
                default:
                    cardDO.setStatus(CardStatusEnum.LOCKED);
                    break;
            }
        }

        // "2025-08-14T06:51:17.638Z" 转换为 "2025-08-14 14:51:17"
        cardDO.setOpenTime(LocalDateTimeUtil.parse(item.getCreateTime(), "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
                .plusHours(8));
        return cardDO;
    }

    @Override
    public CardDO getCardDetail(String cardId) {
        try {
            ItlCardDetailResp itlCardDetailResp = getClient().getCardDetail(cardId);
            if(null != itlCardDetailResp) {
                return convertCard(itlCardDetailResp);
            }


        } catch (ThirdException e) {
            log.error("获取卡片信息失败，cardId: {}", cardId, e);
            if (e.getMessage().contains("token") || e.getMessage().contains("Token")) {
                redisTemplate.delete(itlTokenKey);
                return getCardDetail(cardId);
            }
        }

        return null;
    }

    @Override
    public CardDO getCardSensitiveDetail(String cardId) {
        try {
            ItlCardSensitiveInfoResp resp = getClient().getCardSensitiveInfo(cardId);
            if (resp != null) {
                CardDO cardDO = new CardDO();
                cardDO.setCardNumber(resp.getCardNo());
                cardDO.setCvv(resp.getCvv());
                cardDO.setExpireDate(resp.getExpMonth() + "/" + resp.getExpYear().replaceFirst("20", ""));
                return cardDO;
            }
        } catch (ThirdException e) {
            log.error("获取卡片敏感信息失败，cardId: {}", cardId, e);
            if (e.getMessage().contains("token") || e.getMessage().contains("Token")) {
                redisTemplate.delete(itlTokenKey);
                return getCardSensitiveDetail(cardId);
            }
        }
        return null;
    }

    @Override
    public List<CardBalanceDO> getCardBalanceList(LocalDateTime start, LocalDateTime end) {
        return List.of();
    }

    @Override
    public List<CardTransactionDO> getCardTransactionList(LocalDateTime start, LocalDateTime end, String status) {
        log.info("======【{}】开始同步卡片交易数据======", getCardPlatform().getDescription());
        int page = 0;
        int totalPages = 0;
        if (start == null) {
            start = LocalDateTimeUtil.parse("2025-07-15 00:00:00", "yyyy-MM-dd HH:mm:ss");
        }
        if (end == null) {
            end = LocalDateTimeUtil.now();
        }

        //查询使用中国时间
//
//        start = start.minusHours(8);
//        end = end.minusHours(8);

        List<CardTransactionDO> list = new ArrayList<>();

        while (page <= totalPages) {
            log.info("【{}】开始同步第{}页数据...", getCardPlatform().getDescription(), page);
            try {
                ItlCardTransactionListResp result = getClient().getCardTransactions(null, null, null, LocalDateTimeUtil.format(start, "yyyy-MM-dd HH:mm:ss"),
                        LocalDateTimeUtil.format(end, "yyyy-MM-dd HH:mm:ss"), DEFAULT_PAGE_SIZE, page);

                list.addAll(this.convertCardTransactionList(result.getData()));

                totalPages = result.getPageTotal();
                page++;
            } catch (ThirdException e) {
                if (e.getMessage().contains("token") || e.getMessage().contains("Token")) {
                    log.error("Interace token失效");
                    RedisUtils.delete(itlTokenKey);
                }
                log.info("【{}】第{}页数据同步错误：code={}, message={}", getCardPlatform().getDescription(), page, e.getCode(), e.getMessage());
                break;
            }
        }

        log.info("======【{}】卡片交易数据完成======", getCardPlatform().getDescription());
        return list;
    }

    @Override
    public void rechargeCard(CardDO cardDO, BigDecimal amount) {
        try {
            //获取卡片额度，进行累加
            ItlCardDetailResp cardDetail = getClient().getCardDetail(cardDO.getPlatformCardId());
            if(null == cardDetail) {
                throw new ThirdException(Map.of("code", "500", "msg", "卡片不存在"));
            }

            BigDecimal rechargeAmount = null != cardDetail.getVelocityControl() && StrUtil.isNotBlank(cardDetail.getVelocityControl().getLimit()) ? amount.add(new BigDecimal(cardDetail.getVelocityControl().getLimit())) : amount;
            ItlUpdateCardLimitReq req = new ItlUpdateCardLimitReq();
            req.setCardId(cardDO.getPlatformCardId());
            req.setType("LIFETIME");
            req.setLimit(rechargeAmount.toString());
            getClient().updateCardLimit(req);
        } catch (ThirdException e) {
            log.error("充值卡片失败，cardId: {}, amount: {}", cardDO.getPlatformCardId(), amount, e);
            if (e.getMessage().contains("token") || e.getMessage().contains("Token")) {
                redisTemplate.delete(itlTokenKey);
                rechargeCard(cardDO, amount);
            }
        }
    }

    @Override
    public BigDecimal withdrawCard(CardDO cardDO, BigDecimal amount) {
        try {
            String cardNumber = cardDO.getCardNumber();
            BigDecimal withdrawAmount;
            log.info("======【{}】开始提现卡片{}======", getCardPlatform().getDescription(), cardNumber);

            ItlCardDetailResp cardDetail = getClient().getCardDetail(cardDO.getPlatformCardId());

            log.info("【{}】卡片{}当前余额：{}", getCardPlatform().getDescription(), cardNumber, cardDetail.getBalance().getAvailable());

            if (null == cardDetail.getVelocityControl() || StrUtil.isBlank(cardDetail.getVelocityControl().getLimit()) || null == cardDetail.getBalance()) {
                log.info("【{}】卡片{}没有限额或没有余额，无法提现", getCardPlatform().getDescription(), cardNumber);
                return BigDecimal.ZERO;
            }

            BigDecimal limit = new BigDecimal(cardDetail.getVelocityControl().getLimit());
            BigDecimal balance = new BigDecimal(cardDetail.getBalance().getAvailable());
            if (balance.compareTo(BigDecimal.ZERO) <= 0) {
                log.info("【{}】卡片{}余额不大于零，无法提现", getCardPlatform().getDescription(), cardNumber);
                return BigDecimal.ZERO;
            }

            if(null == amount) {
                withdrawAmount = balance;
            }else {
                if(amount.compareTo(balance) > 0) {
                    log.info("【{}】卡片{}提现金额大于余额，无法提现", getCardPlatform().getDescription(), cardNumber);
                    return BigDecimal.ZERO;
                }

                withdrawAmount = amount;
            }

            ItlUpdateCardLimitReq updateReq = new ItlUpdateCardLimitReq();
            updateReq.setCardId(cardDO.getPlatformCardId());
            updateReq.setType("LIFETIME");
            updateReq.setLimit(limit.subtract(withdrawAmount).toString());
            getClient().updateCardLimit(updateReq);

            return withdrawAmount;
        } catch (ThirdException e) {
            log.error("提现卡片失败，cardId: {}, amount: {}", cardDO.getPlatformCardId(), amount, e);
            if (e.getMessage().contains("token") || e.getMessage().contains("Token")) {
                redisTemplate.delete(itlTokenKey);
                return withdrawCard(cardDO, amount);
            }
        }catch (Exception e) {
            log.error("提现卡片失败，cardId: {}, amount: {}", cardDO.getPlatformCardId(), amount, e);
            throw e;
        }

        return null;
    }

    @Override
    public CardDO openCard(JSONObject data) {
        try {
            ItlCardOpenReq req = new ItlCardOpenReq();
            req.setType("BudgetCard");
            req.setBin(data.getString("cardBin"));
            req.setBatchCount(1);
            req.setUseType("Online advertising");
            req.setPhoneCode("86");
            req.setLabel(data.getString("platformAdId"));
            req.setPhoneNumber("13800000000");
            req.setCardMode("VirtualCard");
            req.setBudgetId("7c668f5f-3deb-43e7-b966-73ac47e547c6");
            req.setCardholderId("1955841292441989122");

            Boolean result = getClient().openCard(req);
            if(!result) {
                throw new ThirdException(Map.of("code", "500", "msg", "开卡失败"));
            }
        } catch (ThirdException e) {
            log.error("开卡失败", e);
            if (e.getMessage().contains("token") || e.getMessage().contains("Token")) {
                redisTemplate.delete(itlTokenKey);
                return openCard(data);
            }

            throw e;
        }

        return null;
    }

    @Override
    public String getVerifyCode(CardDO card) {
        try {
            // 从Redis获取OTP列表
            List<JSONObject> otpList = RedisUtils.getList(ITL_3DS_CODE_KEY);
            if (otpList == null || otpList.isEmpty()) {
                log.warn("Redis中未找到OTP信息");
                return "";
            }

            // 查找匹配cardId的最近一条记录
            for (JSONObject otpInfo : otpList) {
                String cardId = otpInfo.getString("cardId");
                if (card.getPlatformCardId().equals(cardId)) {
                    return otpInfo.getString("otp");
                }
            }

            log.warn("未找到匹配的OTP信息，cardId: {}", card.getPlatformCardId());
            return "";
        } catch (Exception e) {
            log.error("获取卡片验证码失败，cardId: {}", card.getPlatformCardId(), e);
            return "";
        }
    }

    @Override
    public void updateRemark(CardDO card) {
        try {
            // Interlace可能没有更新备注的接口
            log.warn("Interlace卡台暂不支持更新备注功能");
        } catch (Exception e) {
            log.error("更新卡片备注失败，cardId: {}", card.getPlatformCardId(), e);
        }
    }

    @Override
    public List<LabelValueResp<String>> getCardBinList() {
        List<LabelValueResp<String>> cardBinList = new ArrayList<>();
        LabelValueResp<String> item = new LabelValueResp<>();
        item.setLabel("49387519");
//        item.setLabel("49387520");
        item.setValue("49387519");
//        item.setValue("49387520");
        cardBinList.add(item);
        return cardBinList;
    }


    /**
     * 转换数据
     *
     * @param jsonArray
     * @return
     */
    @Override
    public List<CardTransactionDO> convertCardTransactionList(JSONArray jsonArray) {
        List<ItlCardTransactionResp> list = jsonArray.toJavaList(ItlCardTransactionResp.class);
        return this.convertCardTransactionList(list);
    }

    private List<CardTransactionDO> convertCardTransactionList(List<ItlCardTransactionResp> data) {
        List<CardTransactionDO> transactionList = new ArrayList<>();
        if (CollUtil.isEmpty(data)) {
            return transactionList;
        }

        data.forEach(transaction -> {
            //去掉交易授权费
            if(!"Fee_Consumption".equals(transaction.getType())) {
                CardTransactionDO transactionDO = new CardTransactionDO();
                transactionDO.setCardNumber(StrUtil.EMPTY);
                transactionDO.setPlatformCardId(transaction.getCardId());
                transactionDO.setTransactionId(transaction.getClientTransactionId());
                transactionDO.setTransAmount(BigDecimal.valueOf(transaction.getAmount()));
                transactionDO.setTransCurrency(transaction.getCurrency());
                //2025-08-15T08:50:04.510Z 返回的时间少了8小时
                transactionDO.setTransTime(LocalDateTimeUtil.parse(transaction.getTransactionTime(), "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"));

                transactionDO.setChinaTime(null != transactionDO.getTransTime()
                        ? transactionDO.getTransTime().plusHours(8)
                        : null);

                transactionDO.setTransType(getCardTransactionType(transaction.getType()));
                transactionDO.setTransStatus(getCardTransactionStatus(transaction.getStatus()));
                transactionDO.setPlatform(getCardPlatform());
                transactionDO.setTransDetail(transaction.getDetail());
                transactionDO.setOriginTransType(transaction.getType() + "_" + transaction.getStatus());
                transactionDO.setOriginTransactionId(transaction.getRelatedCardTransactionId());
                transactionDO.setRemark(transaction.getRemark());
                transactionList.add(transactionDO);
            }

        });

        return transactionList;
    }

    @Override
    public void activeCard(CardDO cardDO) {
        try {
            ItlEnableCardReq req = new ItlEnableCardReq();
            req.setCardId(cardDO.getPlatformCardId());
            req.setClientTransactionId("ACTIVATE_" + System.currentTimeMillis());
            getClient().enableCard(req);
        } catch (ThirdException e) {
            log.error("激活卡片失败，cardId: {}", cardDO.getPlatformCardId(), e);
            if (e.getMessage().contains("token") || e.getMessage().contains("Token")) {
                redisTemplate.delete(itlTokenKey);
                activeCard(cardDO);
            }
            throw e;
        }
    }

    @Override
    public void inactiveCard(CardDO cardDO) {
        try {
            ItlFreezeCardReq req = new ItlFreezeCardReq();
            req.setCardId(cardDO.getPlatformCardId());
            req.setClientTransactionId("FREEZE_" + System.currentTimeMillis());
            getClient().freezeCard(req);
        } catch (ThirdException e) {
            log.error("冻结卡片失败，cardId: {}", cardDO.getPlatformCardId(), e);
            if (e.getMessage().contains("token") || e.getMessage().contains("Token")) {
                redisTemplate.delete(itlTokenKey);
                inactiveCard(cardDO);
            }
            throw e;
        }
    }

    @Override
    public CardDO getApplyCardResult(String requestId) {
        return null;
    }

    @Override
    public BigDecimal getCurrentBalance() {
        try {
            // 获取所有卡片的余额总和
            List<CardDO> cardList = getCardList(null, null, null);
            BigDecimal totalBalance = BigDecimal.ZERO;
            for (CardDO card : cardList) {
                if (card.getBalance() != null) {
                    totalBalance = totalBalance.add(card.getBalance());
                }
            }
            return totalBalance;
        } catch (Exception e) {
            log.error("获取当前余额失败", e);
            return BigDecimal.ZERO;
        }
    }

    @Override
    public BigDecimal getCardBalance(CardDO cardDO) {
        try {
            CardDO cardDetail = getCardDetail(cardDO.getPlatformCardId());
            return cardDetail != null ? cardDetail.getBalance() : BigDecimal.ZERO;
        } catch (Exception e) {
            log.error("获取卡片余额失败，cardId: {}", cardDO.getPlatformCardId(), e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 获取客户端（带token缓存）
     */
    private ItlClient getClient() {
        try {
            String token = redisTemplate.opsForValue().get(itlTokenKey);
            if (StrUtil.isBlank(token)) {
                synchronized (lock) {
                    token = redisTemplate.opsForValue().get(itlTokenKey);
                    if (StrUtil.isBlank(token)) {
                        ItlAuthCodeResp autoCode = client.getAuthCode();

                        ItlTokenResp tokenResp = client.getToken(autoCode.getCode());
                        if (tokenResp != null && StrUtil.isNotBlank(tokenResp.getAccessToken())) {
                            token = tokenResp.getAccessToken();
                            redisTemplate.opsForValue().set(itlTokenKey, token, tokenResp.getExpiresIn(), TimeUnit.SECONDS);
                        }
                    }
                }
            }
            if (StrUtil.isNotBlank(token)) {
                client.setAccessToken(token);
            }
            return client;
        } catch (Exception e) {
            log.error("获取ItlClient失败", e);
            return client;
        }
    }

    /**
     * 获取卡片余额类型
     */
    private String getCardBalanceType(String type) {
        if (StrUtil.isBlank(type)) {
            return "OTHER";
        }
        switch (type.toLowerCase()) {
            case "TransferIn":
                return "RECHARGE";
            case "TransferOut":
                return "WITHDRAW";
            default:
                return "OTHER";
        }
    }

    /**
     * 获取交易类型
     */
    private CardTransactionTypeEnum getCardTransactionType(String type) {
        if (StrUtil.isBlank(type)) {
            return CardTransactionTypeEnum.OTHER;
        }

        //Consumption TransferIn TransferOut Credit Reversal Frozen UnFrozen
        switch (type) {
            case "Consumption":
                return CardTransactionTypeEnum.AUTHORIZATION;
            case "Reversal":
                return CardTransactionTypeEnum.AUTHORIZATION_BACK;
            default:
                return CardTransactionTypeEnum.OTHER;
        }
    }

    /**
     * 获取交易状态
     */
    private CardTransactionStatusEnum getCardTransactionStatus(String status) {
        if (StrUtil.isBlank(status)) {
            return CardTransactionStatusEnum.OTHER;
        }
        //Pending Closed Fail
        switch (status) {
            case "Pending":
            case "Closed":
                return CardTransactionStatusEnum.SUCCESS;
            case "Fail":
                return CardTransactionStatusEnum.FAIL;
            default:
                return CardTransactionStatusEnum.OTHER;
        }
    }


}