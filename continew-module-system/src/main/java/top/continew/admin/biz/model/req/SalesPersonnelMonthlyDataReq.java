package top.continew.admin.biz.model.req;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改商务人员月度绩效数据参数
 *
 * <AUTHOR>
 * @since 2025/08/20 10:43
 */
@Data
@Schema(description = "创建或修改商务人员月度绩效数据参数")
public class SalesPersonnelMonthlyDataReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联商务
     */
    @Schema(description = "关联商务")
    @NotNull(message = "关联商务不能为空")
    private Long businessUserId;

    /**
     * 岗位类型
     */
    @Schema(description = "岗位类型")
    @NotNull(message = "岗位类型不能为空")
    private Integer jobRank;

    /**
     * 统计月份
     */
    @Schema(description = "统计月份")
    @NotNull(message = "统计月份不能为空")
    private LocalDate salesDate;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @NotNull(message = "创建时间不能为空")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @NotNull(message = "创建人不能为空")
    private Long createUser;
}